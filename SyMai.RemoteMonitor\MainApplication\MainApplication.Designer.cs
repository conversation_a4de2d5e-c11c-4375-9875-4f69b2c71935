﻿namespace SiMay.RemoteMonitor.MainApplication
{
    partial class MainApplication
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.cmdContext = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.toolStripMenuItem6 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.发送信息ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.备注更改ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem7 = new System.Windows.Forms.ToolStripMenuItem();
            this.会话管理ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.updateClient = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem8 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator6 = new System.Windows.Forms.ToolStripSeparator();
            this.关闭计算机ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.重启计算机ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator8 = new System.Windows.Forms.ToolStripSeparator();
            this.installServiceMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.unInstallServiceMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.开机启动ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.取消自启动ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.隐藏服务端ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.卸载控制端ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem5 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.选择全部ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.取消选择ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.logsContext = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.复制日志ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.删除日志ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.清空日志ToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.desktopViewLayout = new System.Windows.Forms.FlowLayoutPanel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.linkLabel2 = new System.Windows.Forms.LinkLabel();
            this.label3 = new System.Windows.Forms.Label();
            this.groupBox = new System.Windows.Forms.ComboBox();
            this.button2 = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.viewColumn = new System.Windows.Forms.Label();
            this.fsd = new System.Windows.Forms.Label();
            this.viewRow = new System.Windows.Forms.Label();
            this.columntrackBar = new System.Windows.Forms.TrackBar();
            this.linkLabel1 = new System.Windows.Forms.LinkLabel();
            this.label1 = new System.Windows.Forms.Label();
            this.rowtrackBar = new System.Windows.Forms.TrackBar();
            this.splitContainer = new System.Windows.Forms.SplitContainer();
            this.tabControl2 = new System.Windows.Forms.TabControl();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.logList = new SiMay.RemoteMonitor.UserControls.UListView();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.servicesOnlineList = new SiMay.RemoteMonitor.UserControls.UListView();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
            this.stripHost = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabel5 = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabel2 = new System.Windows.Forms.ToolStripStatusLabel();
            this.struflow = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabel4 = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabel7 = new System.Windows.Forms.ToolStripStatusLabel();
            this.strdownflow = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabel8 = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabel3 = new System.Windows.Forms.ToolStripStatusLabel();
            this.stripPort = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabel6 = new System.Windows.Forms.ToolStripStatusLabel();
            this.stripConnectedNum = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.toolStripButton10 = new System.Windows.Forms.ToolStripButton();
            this.toolStripButton9 = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator5 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripButton7 = new System.Windows.Forms.ToolStripButton();
            this.toolStripButton6 = new System.Windows.Forms.ToolStripButton();
            this.toolStripButton14 = new System.Windows.Forms.ToolStripButton();
            this.toolStripButton8 = new System.Windows.Forms.ToolStripButton();
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.系统设置ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.创建客户ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.查看ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.statusToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.onlineToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.锁定ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.cmdContext.SuspendLayout();
            this.logsContext.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.columntrackBar)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rowtrackBar)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).BeginInit();
            this.splitContainer.Panel1.SuspendLayout();
            this.splitContainer.Panel2.SuspendLayout();
            this.splitContainer.SuspendLayout();
            this.tabControl2.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            this.menuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // cmdContext
            // 
            this.cmdContext.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.cmdContext.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem6,
            this.toolStripMenuItem1,
            this.发送信息ToolStripMenuItem,
            this.备注更改ToolStripMenuItem,
            this.toolStripMenuItem7,
            this.会话管理ToolStripMenuItem,
            this.toolStripSeparator4,
            this.toolStripMenuItem4,
            this.toolStripMenuItem5,
            this.toolStripSeparator1,
            this.选择全部ToolStripMenuItem,
            this.取消选择ToolStripMenuItem});
            this.cmdContext.Name = "CmdContext";
            this.cmdContext.RenderMode = System.Windows.Forms.ToolStripRenderMode.Professional;
            this.cmdContext.Size = new System.Drawing.Size(154, 256);
            this.cmdContext.Opening += new System.ComponentModel.CancelEventHandler(this.CmdContext_Opening);
            // 
            // toolStripMenuItem6
            // 
            this.toolStripMenuItem6.Name = "toolStripMenuItem6";
            this.toolStripMenuItem6.Size = new System.Drawing.Size(153, 24);
            this.toolStripMenuItem6.Text = "打开网页";
            this.toolStripMenuItem6.Click += new System.EventHandler(this.toolStripMenuItem6_Click_1);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(153, 24);
            this.toolStripMenuItem1.Text = "下载管理";
            this.toolStripMenuItem1.Click += new System.EventHandler(this.RemoteDownloadExecete);
            // 
            // 发送信息ToolStripMenuItem
            // 
            this.发送信息ToolStripMenuItem.Name = "发送信息ToolStripMenuItem";
            this.发送信息ToolStripMenuItem.Size = new System.Drawing.Size(153, 24);
            this.发送信息ToolStripMenuItem.Text = "发送信息";
            this.发送信息ToolStripMenuItem.Click += new System.EventHandler(this.SendMessageBox);
            // 
            // 备注更改ToolStripMenuItem
            // 
            this.备注更改ToolStripMenuItem.Name = "备注更改ToolStripMenuItem";
            this.备注更改ToolStripMenuItem.Size = new System.Drawing.Size(153, 24);
            this.备注更改ToolStripMenuItem.Text = "备注更改";
            this.备注更改ToolStripMenuItem.Click += new System.EventHandler(this.ModifyRemark);
            // 
            // toolStripMenuItem7
            // 
            this.toolStripMenuItem7.Name = "toolStripMenuItem7";
            this.toolStripMenuItem7.Size = new System.Drawing.Size(153, 24);
            this.toolStripMenuItem7.Text = "分组更改";
            this.toolStripMenuItem7.Click += new System.EventHandler(this.ToolStripMenuItem7_Click);
            // 
            // 会话管理ToolStripMenuItem
            // 
            this.会话管理ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.updateClient,
            this.toolStripMenuItem8,
            this.toolStripSeparator6,
            this.关闭计算机ToolStripMenuItem,
            this.重启计算机ToolStripMenuItem,
            this.toolStripSeparator8,
            this.installServiceMenuItem,
            this.unInstallServiceMenuItem,
            this.toolStripSeparator2,
            this.开机启动ToolStripMenuItem,
            this.取消自启动ToolStripMenuItem,
            this.隐藏服务端ToolStripMenuItem,
            this.toolStripMenuItem2,
            this.toolStripSeparator3,
            this.卸载控制端ToolStripMenuItem});
            this.会话管理ToolStripMenuItem.Name = "会话管理ToolStripMenuItem";
            this.会话管理ToolStripMenuItem.Size = new System.Drawing.Size(153, 24);
            this.会话管理ToolStripMenuItem.Text = "会话管理";
            // 
            // updateClient
            // 
            this.updateClient.Name = "updateClient";
            this.updateClient.Size = new System.Drawing.Size(224, 26);
            this.updateClient.Text = "远程更新";
            this.updateClient.Click += new System.EventHandler(this.UpdateClient_Click);
            // 
            // toolStripMenuItem8
            // 
            this.toolStripMenuItem8.Name = "toolStripMenuItem8";
            this.toolStripMenuItem8.Size = new System.Drawing.Size(224, 26);
            this.toolStripMenuItem8.Text = "重新载入";
            this.toolStripMenuItem8.Click += new System.EventHandler(this.ToolStripMenuItem8_Click);
            // 
            // toolStripSeparator6
            // 
            this.toolStripSeparator6.Name = "toolStripSeparator6";
            this.toolStripSeparator6.Size = new System.Drawing.Size(221, 6);
            // 
            // 关闭计算机ToolStripMenuItem
            // 
            this.关闭计算机ToolStripMenuItem.Name = "关闭计算机ToolStripMenuItem";
            this.关闭计算机ToolStripMenuItem.Size = new System.Drawing.Size(224, 26);
            this.关闭计算机ToolStripMenuItem.Text = "关闭计算机";
            this.关闭计算机ToolStripMenuItem.Click += new System.EventHandler(this.RemoteShutdown);
            // 
            // 重启计算机ToolStripMenuItem
            // 
            this.重启计算机ToolStripMenuItem.Name = "重启计算机ToolStripMenuItem";
            this.重启计算机ToolStripMenuItem.Size = new System.Drawing.Size(224, 26);
            this.重启计算机ToolStripMenuItem.Text = "重启计算机";
            this.重启计算机ToolStripMenuItem.Click += new System.EventHandler(this.RemoteReboot);
            // 
            // toolStripSeparator8
            // 
            this.toolStripSeparator8.Name = "toolStripSeparator8";
            this.toolStripSeparator8.Size = new System.Drawing.Size(221, 6);
            // 
            // installServiceMenuItem
            // 
            this.installServiceMenuItem.Name = "installServiceMenuItem";
            this.installServiceMenuItem.Size = new System.Drawing.Size(224, 26);
            this.installServiceMenuItem.Text = "服务安装";
            this.installServiceMenuItem.Click += new System.EventHandler(this.installServiceMenuItem_Click);
            // 
            // unInstallServiceMenuItem
            // 
            this.unInstallServiceMenuItem.Name = "unInstallServiceMenuItem";
            this.unInstallServiceMenuItem.Size = new System.Drawing.Size(224, 26);
            this.unInstallServiceMenuItem.Text = "服务卸载";
            this.unInstallServiceMenuItem.Click += new System.EventHandler(this.unInstallServiceMenuItem_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(221, 6);
            // 
            // 开机启动ToolStripMenuItem
            // 
            this.开机启动ToolStripMenuItem.Name = "开机启动ToolStripMenuItem";
            this.开机启动ToolStripMenuItem.Size = new System.Drawing.Size(224, 26);
            this.开机启动ToolStripMenuItem.Text = "注册表启动";
            this.开机启动ToolStripMenuItem.Click += new System.EventHandler(this.RemoteStartup);
            // 
            // 取消自启动ToolStripMenuItem
            // 
            this.取消自启动ToolStripMenuItem.Name = "取消自启动ToolStripMenuItem";
            this.取消自启动ToolStripMenuItem.Size = new System.Drawing.Size(224, 26);
            this.取消自启动ToolStripMenuItem.Text = "取消自启动";
            this.取消自启动ToolStripMenuItem.Click += new System.EventHandler(this.RemoteUnStarup);
            // 
            // 隐藏服务端ToolStripMenuItem
            // 
            this.隐藏服务端ToolStripMenuItem.Name = "隐藏服务端ToolStripMenuItem";
            this.隐藏服务端ToolStripMenuItem.Size = new System.Drawing.Size(224, 26);
            this.隐藏服务端ToolStripMenuItem.Text = "隐藏服务端";
            this.隐藏服务端ToolStripMenuItem.Click += new System.EventHandler(this.RemoteHideServiceFile);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(224, 26);
            this.toolStripMenuItem2.Text = "显示服务端";
            this.toolStripMenuItem2.Click += new System.EventHandler(this.ToolStripMenuItem2_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(221, 6);
            // 
            // 卸载控制端ToolStripMenuItem
            // 
            this.卸载控制端ToolStripMenuItem.Name = "卸载控制端ToolStripMenuItem";
            this.卸载控制端ToolStripMenuItem.Size = new System.Drawing.Size(224, 26);
            this.卸载控制端ToolStripMenuItem.Text = "卸载服务端";
            this.卸载控制端ToolStripMenuItem.Click += new System.EventHandler(this.UninstallService);
            // 
            // toolStripSeparator4
            // 
            this.toolStripSeparator4.Name = "toolStripSeparator4";
            this.toolStripSeparator4.Size = new System.Drawing.Size(150, 6);
            // 
            // toolStripMenuItem4
            // 
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            this.toolStripMenuItem4.Size = new System.Drawing.Size(153, 24);
            this.toolStripMenuItem4.Text = "开启屏幕墙";
            this.toolStripMenuItem4.Click += new System.EventHandler(this.toolStripMenuItem4_Click);
            // 
            // toolStripMenuItem5
            // 
            this.toolStripMenuItem5.Name = "toolStripMenuItem5";
            this.toolStripMenuItem5.Size = new System.Drawing.Size(153, 24);
            this.toolStripMenuItem5.Text = "关闭屏幕墙";
            this.toolStripMenuItem5.Click += new System.EventHandler(this.toolStripMenuItem5_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(150, 6);
            // 
            // 选择全部ToolStripMenuItem
            // 
            this.选择全部ToolStripMenuItem.Name = "选择全部ToolStripMenuItem";
            this.选择全部ToolStripMenuItem.Size = new System.Drawing.Size(153, 24);
            this.选择全部ToolStripMenuItem.Text = "选择全部";
            this.选择全部ToolStripMenuItem.Click += new System.EventHandler(this.OnlineList_OnSelected);
            // 
            // 取消选择ToolStripMenuItem
            // 
            this.取消选择ToolStripMenuItem.Name = "取消选择ToolStripMenuItem";
            this.取消选择ToolStripMenuItem.Size = new System.Drawing.Size(153, 24);
            this.取消选择ToolStripMenuItem.Text = "取消选择";
            this.取消选择ToolStripMenuItem.Click += new System.EventHandler(this.OnileList_OnUnSelected);
            // 
            // logsContext
            // 
            this.logsContext.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.logsContext.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.复制日志ToolStripMenuItem,
            this.删除日志ToolStripMenuItem,
            this.清空日志ToolStripMenuItem1});
            this.logsContext.Name = "logsContext";
            this.logsContext.RenderMode = System.Windows.Forms.ToolStripRenderMode.Professional;
            this.logsContext.Size = new System.Drawing.Size(139, 76);
            // 
            // 复制日志ToolStripMenuItem
            // 
            this.复制日志ToolStripMenuItem.Name = "复制日志ToolStripMenuItem";
            this.复制日志ToolStripMenuItem.Size = new System.Drawing.Size(138, 24);
            this.复制日志ToolStripMenuItem.Text = "复制日志";
            this.复制日志ToolStripMenuItem.Click += new System.EventHandler(this.CopyRuningLog);
            // 
            // 删除日志ToolStripMenuItem
            // 
            this.删除日志ToolStripMenuItem.Name = "删除日志ToolStripMenuItem";
            this.删除日志ToolStripMenuItem.Size = new System.Drawing.Size(138, 24);
            this.删除日志ToolStripMenuItem.Text = "删除日志";
            this.删除日志ToolStripMenuItem.Click += new System.EventHandler(this.DeleteRuningLog);
            // 
            // 清空日志ToolStripMenuItem1
            // 
            this.清空日志ToolStripMenuItem1.Name = "清空日志ToolStripMenuItem1";
            this.清空日志ToolStripMenuItem1.Size = new System.Drawing.Size(138, 24);
            this.清空日志ToolStripMenuItem1.Text = "清空日志";
            this.清空日志ToolStripMenuItem1.Click += new System.EventHandler(this.ClearRuningLog);
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.Location = new System.Drawing.Point(0, 87);
            this.splitContainer1.Margin = new System.Windows.Forms.Padding(4);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.desktopViewLayout);
            this.splitContainer1.Panel1.Controls.Add(this.panel1);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.splitContainer);
            this.splitContainer1.Size = new System.Drawing.Size(1476, 825);
            this.splitContainer1.SplitterDistance = 596;
            this.splitContainer1.SplitterWidth = 1;
            this.splitContainer1.TabIndex = 7;
            // 
            // desktopViewLayout
            // 
            this.desktopViewLayout.AutoScroll = true;
            this.desktopViewLayout.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.desktopViewLayout.Dock = System.Windows.Forms.DockStyle.Fill;
            this.desktopViewLayout.Location = new System.Drawing.Point(0, 0);
            this.desktopViewLayout.Margin = new System.Windows.Forms.Padding(0);
            this.desktopViewLayout.Name = "desktopViewLayout";
            this.desktopViewLayout.Size = new System.Drawing.Size(1476, 566);
            this.desktopViewLayout.TabIndex = 3;
            this.desktopViewLayout.Resize += new System.EventHandler(this.desktopViewLayout_Resize);
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.linkLabel2);
            this.panel1.Controls.Add(this.label3);
            this.panel1.Controls.Add(this.groupBox);
            this.panel1.Controls.Add(this.button2);
            this.panel1.Controls.Add(this.button1);
            this.panel1.Controls.Add(this.viewColumn);
            this.panel1.Controls.Add(this.fsd);
            this.panel1.Controls.Add(this.viewRow);
            this.panel1.Controls.Add(this.columntrackBar);
            this.panel1.Controls.Add(this.linkLabel1);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Controls.Add(this.rowtrackBar);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel1.Location = new System.Drawing.Point(0, 566);
            this.panel1.Margin = new System.Windows.Forms.Padding(4);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1476, 30);
            this.panel1.TabIndex = 4;
            // 
            // linkLabel2
            // 
            this.linkLabel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.linkLabel2.AutoSize = true;
            this.linkLabel2.Location = new System.Drawing.Point(1397, 8);
            this.linkLabel2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.linkLabel2.Name = "linkLabel2";
            this.linkLabel2.Size = new System.Drawing.Size(67, 15);
            this.linkLabel2.TabIndex = 13;
            this.linkLabel2.TabStop = true;
            this.linkLabel2.Text = "视图设置";
            this.linkLabel2.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel2_LinkClicked);
            // 
            // label3
            // 
            this.label3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(723, 8);
            this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(45, 15);
            this.label3.TabIndex = 12;
            this.label3.Text = "分组:";
            // 
            // groupBox
            // 
            this.groupBox.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.groupBox.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.groupBox.FormattingEnabled = true;
            this.groupBox.Items.AddRange(new object[] {
            "全部"});
            this.groupBox.Location = new System.Drawing.Point(771, 4);
            this.groupBox.Margin = new System.Windows.Forms.Padding(4);
            this.groupBox.Name = "groupBox";
            this.groupBox.Size = new System.Drawing.Size(152, 23);
            this.groupBox.TabIndex = 11;
            this.groupBox.SelectedIndexChanged += new System.EventHandler(this.GroupBox_SelectedIndexChanged);
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(88, 2);
            this.button2.Margin = new System.Windows.Forms.Padding(4);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(75, 25);
            this.button2.TabIndex = 8;
            this.button2.Text = "反选";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(5, 2);
            this.button1.Margin = new System.Windows.Forms.Padding(4);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(75, 25);
            this.button1.TabIndex = 7;
            this.button1.Text = "全选";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // viewColumn
            // 
            this.viewColumn.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.viewColumn.AutoSize = true;
            this.viewColumn.Location = new System.Drawing.Point(1284, 8);
            this.viewColumn.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.viewColumn.Name = "viewColumn";
            this.viewColumn.Size = new System.Drawing.Size(15, 15);
            this.viewColumn.TabIndex = 6;
            this.viewColumn.Text = "3";
            // 
            // fsd
            // 
            this.fsd.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.fsd.AutoSize = true;
            this.fsd.Location = new System.Drawing.Point(931, 8);
            this.fsd.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.fsd.Name = "fsd";
            this.fsd.Size = new System.Drawing.Size(60, 15);
            this.fsd.TabIndex = 5;
            this.fsd.Text = "视图列:";
            // 
            // viewRow
            // 
            this.viewRow.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.viewRow.AutoSize = true;
            this.viewRow.Location = new System.Drawing.Point(1088, 8);
            this.viewRow.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.viewRow.Name = "viewRow";
            this.viewRow.Size = new System.Drawing.Size(15, 15);
            this.viewRow.TabIndex = 4;
            this.viewRow.Text = "4";
            // 
            // columntrackBar
            // 
            this.columntrackBar.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.columntrackBar.AutoSize = false;
            this.columntrackBar.Location = new System.Drawing.Point(1001, 6);
            this.columntrackBar.Margin = new System.Windows.Forms.Padding(4);
            this.columntrackBar.Maximum = 50;
            this.columntrackBar.Minimum = 1;
            this.columntrackBar.Name = "columntrackBar";
            this.columntrackBar.Size = new System.Drawing.Size(95, 21);
            this.columntrackBar.TabIndex = 3;
            this.columntrackBar.TickStyle = System.Windows.Forms.TickStyle.None;
            this.columntrackBar.Value = 4;
            this.columntrackBar.Scroll += new System.EventHandler(this.RowtrackBar_Scroll);
            // 
            // linkLabel1
            // 
            this.linkLabel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.linkLabel1.AutoSize = true;
            this.linkLabel1.Location = new System.Drawing.Point(1317, 8);
            this.linkLabel1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.linkLabel1.Name = "linkLabel1";
            this.linkLabel1.Size = new System.Drawing.Size(67, 15);
            this.linkLabel1.TabIndex = 2;
            this.linkLabel1.TabStop = true;
            this.linkLabel1.Text = "保存设置";
            this.linkLabel1.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel1_LinkClicked);
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(1119, 8);
            this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(60, 15);
            this.label1.TabIndex = 1;
            this.label1.Text = "视图行:";
            // 
            // rowtrackBar
            // 
            this.rowtrackBar.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.rowtrackBar.AutoSize = false;
            this.rowtrackBar.Location = new System.Drawing.Point(1187, 6);
            this.rowtrackBar.Margin = new System.Windows.Forms.Padding(4);
            this.rowtrackBar.Maximum = 50;
            this.rowtrackBar.Minimum = 1;
            this.rowtrackBar.Name = "rowtrackBar";
            this.rowtrackBar.Size = new System.Drawing.Size(101, 21);
            this.rowtrackBar.TabIndex = 0;
            this.rowtrackBar.TickStyle = System.Windows.Forms.TickStyle.None;
            this.rowtrackBar.Value = 3;
            this.rowtrackBar.Scroll += new System.EventHandler(this.ColumntrackBar_Scroll);
            // 
            // splitContainer
            // 
            this.splitContainer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer.Location = new System.Drawing.Point(0, 0);
            this.splitContainer.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.splitContainer.Name = "splitContainer";
            // 
            // splitContainer.Panel1
            // 
            this.splitContainer.Panel1.Controls.Add(this.tabControl2);
            // 
            // splitContainer.Panel2
            // 
            this.splitContainer.Panel2.Controls.Add(this.tabControl1);
            this.splitContainer.Size = new System.Drawing.Size(1476, 228);
            this.splitContainer.SplitterDistance = 366;
            this.splitContainer.SplitterWidth = 1;
            this.splitContainer.TabIndex = 0;
            // 
            // tabControl2
            // 
            this.tabControl2.Alignment = System.Windows.Forms.TabAlignment.Bottom;
            this.tabControl2.Controls.Add(this.tabPage2);
            this.tabControl2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl2.Location = new System.Drawing.Point(0, 0);
            this.tabControl2.Margin = new System.Windows.Forms.Padding(4);
            this.tabControl2.Name = "tabControl2";
            this.tabControl2.SelectedIndex = 0;
            this.tabControl2.Size = new System.Drawing.Size(366, 228);
            this.tabControl2.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            this.tabControl2.TabIndex = 1;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.logList);
            this.tabPage2.Location = new System.Drawing.Point(4, 4);
            this.tabPage2.Margin = new System.Windows.Forms.Padding(4);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(4);
            this.tabPage2.Size = new System.Drawing.Size(358, 199);
            this.tabPage2.TabIndex = 2;
            this.tabPage2.Text = "运行日志";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // logList
            // 
            this.logList.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.logList.ContextMenuStrip = this.logsContext;
            this.logList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.logList.FullRowSelect = true;
            this.logList.HideSelection = false;
            this.logList.Location = new System.Drawing.Point(4, 4);
            this.logList.Margin = new System.Windows.Forms.Padding(4);
            this.logList.Name = "logList";
            this.logList.ProgressColumnIndex = -1;
            this.logList.Size = new System.Drawing.Size(350, 191);
            this.logList.TabIndex = 0;
            this.logList.UseCompatibleStateImageBehavior = false;
            this.logList.UseWindowsThemStyle = true;
            this.logList.View = System.Windows.Forms.View.Details;
            this.logList.MouseEnter += new System.EventHandler(this.logList_MouseEnter);
            // 
            // tabControl1
            // 
            this.tabControl1.Alignment = System.Windows.Forms.TabAlignment.Bottom;
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Margin = new System.Windows.Forms.Padding(4);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1109, 228);
            this.tabControl1.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            this.tabControl1.TabIndex = 0;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.servicesOnlineList);
            this.tabPage1.Location = new System.Drawing.Point(4, 4);
            this.tabPage1.Margin = new System.Windows.Forms.Padding(4);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(4);
            this.tabPage1.Size = new System.Drawing.Size(1101, 199);
            this.tabPage1.TabIndex = 2;
            this.tabPage1.Text = "在线列表";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // servicesOnlineList
            // 
            this.servicesOnlineList.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.servicesOnlineList.CheckBoxes = true;
            this.servicesOnlineList.ContextMenuStrip = this.cmdContext;
            this.servicesOnlineList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.servicesOnlineList.FullRowSelect = true;
            this.servicesOnlineList.HideSelection = false;
            this.servicesOnlineList.Location = new System.Drawing.Point(4, 4);
            this.servicesOnlineList.Margin = new System.Windows.Forms.Padding(4);
            this.servicesOnlineList.Name = "servicesOnlineList";
            this.servicesOnlineList.ProgressColumnIndex = -1;
            this.servicesOnlineList.Size = new System.Drawing.Size(1093, 191);
            this.servicesOnlineList.TabIndex = 0;
            this.servicesOnlineList.UseCompatibleStateImageBehavior = false;
            this.servicesOnlineList.UseWindowsThemStyle = false;
            this.servicesOnlineList.View = System.Windows.Forms.View.Details;
            this.servicesOnlineList.MouseEnter += new System.EventHandler(this.onlineList_MouseEnter);
            // 
            // statusStrip1
            // 
            this.statusStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabel1,
            this.stripHost,
            this.toolStripStatusLabel5,
            this.toolStripStatusLabel2,
            this.struflow,
            this.toolStripStatusLabel4,
            this.toolStripStatusLabel7,
            this.strdownflow,
            this.toolStripStatusLabel8,
            this.toolStripStatusLabel3,
            this.stripPort,
            this.toolStripStatusLabel6,
            this.stripConnectedNum});
            this.statusStrip1.Location = new System.Drawing.Point(0, 912);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Padding = new System.Windows.Forms.Padding(1, 0, 19, 0);
            this.statusStrip1.RenderMode = System.Windows.Forms.ToolStripRenderMode.ManagerRenderMode;
            this.statusStrip1.Size = new System.Drawing.Size(1476, 30);
            this.statusStrip1.TabIndex = 2;
            this.statusStrip1.Text = "statusStrip1";
            // 
            // toolStripStatusLabel1
            // 
            this.toolStripStatusLabel1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            this.toolStripStatusLabel1.Size = new System.Drawing.Size(88, 24);
            this.toolStripStatusLabel1.Text = "服务器地址:";
            // 
            // stripHost
            // 
            this.stripHost.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.stripHost.Name = "stripHost";
            this.stripHost.Size = new System.Drawing.Size(57, 24);
            this.stripHost.Text = "0.0.0.0";
            // 
            // toolStripStatusLabel5
            // 
            this.toolStripStatusLabel5.Name = "toolStripStatusLabel5";
            this.toolStripStatusLabel5.Size = new System.Drawing.Size(873, 24);
            this.toolStripStatusLabel5.Spring = true;
            // 
            // toolStripStatusLabel2
            // 
            this.toolStripStatusLabel2.Name = "toolStripStatusLabel2";
            this.toolStripStatusLabel2.Size = new System.Drawing.Size(43, 24);
            this.toolStripStatusLabel2.Text = "上传:";
            // 
            // struflow
            // 
            this.struflow.Name = "struflow";
            this.struflow.Size = new System.Drawing.Size(40, 24);
            this.struflow.Text = "0.00";
            // 
            // toolStripStatusLabel4
            // 
            this.toolStripStatusLabel4.Name = "toolStripStatusLabel4";
            this.toolStripStatusLabel4.Size = new System.Drawing.Size(43, 24);
            this.toolStripStatusLabel4.Text = "KB/S";
            // 
            // toolStripStatusLabel7
            // 
            this.toolStripStatusLabel7.Name = "toolStripStatusLabel7";
            this.toolStripStatusLabel7.Size = new System.Drawing.Size(43, 24);
            this.toolStripStatusLabel7.Text = "下传:";
            // 
            // strdownflow
            // 
            this.strdownflow.Name = "strdownflow";
            this.strdownflow.Size = new System.Drawing.Size(40, 24);
            this.strdownflow.Text = "0.00";
            // 
            // toolStripStatusLabel8
            // 
            this.toolStripStatusLabel8.Name = "toolStripStatusLabel8";
            this.toolStripStatusLabel8.Size = new System.Drawing.Size(43, 24);
            this.toolStripStatusLabel8.Text = "KB/S";
            // 
            // toolStripStatusLabel3
            // 
            this.toolStripStatusLabel3.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left;
            this.toolStripStatusLabel3.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.toolStripStatusLabel3.Name = "toolStripStatusLabel3";
            this.toolStripStatusLabel3.Size = new System.Drawing.Size(47, 24);
            this.toolStripStatusLabel3.Text = "端口:";
            // 
            // stripPort
            // 
            this.stripPort.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.stripPort.Name = "stripPort";
            this.stripPort.Size = new System.Drawing.Size(45, 24);
            this.stripPort.Text = "5200";
            // 
            // toolStripStatusLabel6
            // 
            this.toolStripStatusLabel6.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left;
            this.toolStripStatusLabel6.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.toolStripStatusLabel6.Name = "toolStripStatusLabel6";
            this.toolStripStatusLabel6.Size = new System.Drawing.Size(77, 24);
            this.toolStripStatusLabel6.Text = "主机数量:";
            // 
            // stripConnectedNum
            // 
            this.stripConnectedNum.Font = new System.Drawing.Font("微软雅黑", 7.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.stripConnectedNum.Name = "stripConnectedNum";
            this.stripConnectedNum.Size = new System.Drawing.Size(17, 24);
            this.stripConnectedNum.Text = "0";
            // 
            // toolStrip1
            // 
            this.toolStrip1.BackColor = System.Drawing.Color.White;
            this.toolStrip1.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
            this.toolStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripButton10,
            this.toolStripButton9,
            this.toolStripSeparator5,
            this.toolStripButton7,
            this.toolStripButton6,
            this.toolStripButton14,
            this.toolStripButton8});
            this.toolStrip1.Location = new System.Drawing.Point(0, 28);
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.Size = new System.Drawing.Size(1476, 59);
            this.toolStrip1.TabIndex = 6;
            this.toolStrip1.Text = "toolStrip1";
            // 
            // toolStripButton10
            // 
            this.toolStripButton10.Image = global::SiMay.RemoteMonitor.Properties.Resources.Syset;
            this.toolStripButton10.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.toolStripButton10.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton10.Name = "toolStripButton10";
            this.toolStripButton10.Size = new System.Drawing.Size(73, 56);
            this.toolStripButton10.Text = "系统设置";
            this.toolStripButton10.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.toolStripButton10.Click += new System.EventHandler(this.toolStripButton10_Click);
            // 
            // toolStripButton9
            // 
            this.toolStripButton9.Image = global::SiMay.RemoteMonitor.Properties.Resources.CreateService;
            this.toolStripButton9.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.toolStripButton9.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton9.Name = "toolStripButton9";
            this.toolStripButton9.Size = new System.Drawing.Size(73, 56);
            this.toolStripButton9.Text = "创建客户";
            this.toolStripButton9.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.toolStripButton9.Click += new System.EventHandler(this.toolStripButton9_Click);
            // 
            // toolStripSeparator5
            // 
            this.toolStripSeparator5.Name = "toolStripSeparator5";
            this.toolStripSeparator5.Size = new System.Drawing.Size(6, 59);
            // 
            // toolStripButton7
            // 
            this.toolStripButton7.Image = global::SiMay.RemoteMonitor.Properties.Resources.Abort;
            this.toolStripButton7.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.toolStripButton7.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton7.Name = "toolStripButton7";
            this.toolStripButton7.Size = new System.Drawing.Size(73, 56);
            this.toolStripButton7.Text = "消息通知";
            this.toolStripButton7.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.toolStripButton7.Click += new System.EventHandler(this.toolStripButton7_Click);
            // 
            // toolStripButton6
            // 
            this.toolStripButton6.Image = global::SiMay.RemoteMonitor.Properties.Resources.Downloadexc;
            this.toolStripButton6.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.toolStripButton6.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton6.Name = "toolStripButton6";
            this.toolStripButton6.Size = new System.Drawing.Size(73, 56);
            this.toolStripButton6.Text = "下载管理";
            this.toolStripButton6.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.toolStripButton6.Click += new System.EventHandler(this.toolStripButton6_Click);
            // 
            // toolStripButton14
            // 
            this.toolStripButton14.Image = global::SiMay.RemoteMonitor.Properties.Resources.Exit;
            this.toolStripButton14.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.toolStripButton14.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton14.Name = "toolStripButton14";
            this.toolStripButton14.Size = new System.Drawing.Size(73, 56);
            this.toolStripButton14.Text = "关闭屏幕";
            this.toolStripButton14.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.toolStripButton14.Click += new System.EventHandler(this.toolStripButton14_Click);
            // 
            // toolStripButton8
            // 
            this.toolStripButton8.Image = global::SiMay.RemoteMonitor.Properties.Resources.Exit;
            this.toolStripButton8.ImageScaling = System.Windows.Forms.ToolStripItemImageScaling.None;
            this.toolStripButton8.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.toolStripButton8.Name = "toolStripButton8";
            this.toolStripButton8.Size = new System.Drawing.Size(73, 56);
            this.toolStripButton8.Text = "卸载程序";
            this.toolStripButton8.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
            this.toolStripButton8.Click += new System.EventHandler(this.toolStripButton8_Click);
            // 
            // menuStrip1
            // 
            this.menuStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.系统设置ToolStripMenuItem,
            this.创建客户ToolStripMenuItem,
            this.查看ToolStripMenuItem,
            this.锁定ToolStripMenuItem,
            this.toolStripMenuItem3});
            this.menuStrip1.Location = new System.Drawing.Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Padding = new System.Windows.Forms.Padding(5, 2, 0, 2);
            this.menuStrip1.RenderMode = System.Windows.Forms.ToolStripRenderMode.Professional;
            this.menuStrip1.Size = new System.Drawing.Size(1476, 28);
            this.menuStrip1.TabIndex = 0;
            this.menuStrip1.Text = "menuStrip1";
            // 
            // 系统设置ToolStripMenuItem
            // 
            this.系统设置ToolStripMenuItem.Name = "系统设置ToolStripMenuItem";
            this.系统设置ToolStripMenuItem.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.F)));
            this.系统设置ToolStripMenuItem.Size = new System.Drawing.Size(101, 24);
            this.系统设置ToolStripMenuItem.Text = "系统设置(&F)";
            this.系统设置ToolStripMenuItem.Click += new System.EventHandler(this.SystemOption);
            // 
            // 创建客户ToolStripMenuItem
            // 
            this.创建客户ToolStripMenuItem.Name = "创建客户ToolStripMenuItem";
            this.创建客户ToolStripMenuItem.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.E)));
            this.创建客户ToolStripMenuItem.Size = new System.Drawing.Size(101, 24);
            this.创建客户ToolStripMenuItem.Text = "创建客户(&E)";
            this.创建客户ToolStripMenuItem.Click += new System.EventHandler(this.CreateService);
            // 
            // 查看ToolStripMenuItem
            // 
            this.查看ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ToolStripMenuItem,
            this.statusToolStripMenuItem,
            this.onlineToolStripMenuItem});
            this.查看ToolStripMenuItem.Name = "查看ToolStripMenuItem";
            this.查看ToolStripMenuItem.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.V)));
            this.查看ToolStripMenuItem.Size = new System.Drawing.Size(103, 24);
            this.查看ToolStripMenuItem.Text = "视图查看(&V)";
            // 
            // ToolStripMenuItem
            // 
            this.ToolStripMenuItem.Checked = true;
            this.ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ToolStripMenuItem.Name = "ToolStripMenuItem";
            this.ToolStripMenuItem.Size = new System.Drawing.Size(137, 26);
            this.ToolStripMenuItem.Text = "工具栏";
            this.ToolStripMenuItem.Click += new System.EventHandler(this.ToolStripMenuItem_Click);
            // 
            // statusToolStripMenuItem
            // 
            this.statusToolStripMenuItem.Checked = true;
            this.statusToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.statusToolStripMenuItem.Name = "statusToolStripMenuItem";
            this.statusToolStripMenuItem.Size = new System.Drawing.Size(137, 26);
            this.statusToolStripMenuItem.Text = "状态栏";
            this.statusToolStripMenuItem.Click += new System.EventHandler(this.statusToolStripMenuItem_Click);
            // 
            // onlineToolStripMenuItem
            // 
            this.onlineToolStripMenuItem.Checked = true;
            this.onlineToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.onlineToolStripMenuItem.Name = "onlineToolStripMenuItem";
            this.onlineToolStripMenuItem.Size = new System.Drawing.Size(137, 26);
            this.onlineToolStripMenuItem.Text = "选项栏";
            this.onlineToolStripMenuItem.Click += new System.EventHandler(this.onlineToolStripMenuItem_Click);
            // 
            // 锁定ToolStripMenuItem
            // 
            this.锁定ToolStripMenuItem.Name = "锁定ToolStripMenuItem";
            this.锁定ToolStripMenuItem.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.G)));
            this.锁定ToolStripMenuItem.Size = new System.Drawing.Size(74, 24);
            this.锁定ToolStripMenuItem.Text = "锁定(&G)";
            this.锁定ToolStripMenuItem.Click += new System.EventHandler(this.lockToolStripMenuItem_Click);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.ShortcutKeys = ((System.Windows.Forms.Keys)((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.H)));
            this.toolStripMenuItem3.Size = new System.Drawing.Size(105, 24);
            this.toolStripMenuItem3.Text = "关于程序(&H)";
            this.toolStripMenuItem3.Click += new System.EventHandler(this.About);
            // 
            // MainApplication
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1476, 942);
            this.Controls.Add(this.splitContainer1);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.toolStrip1);
            this.Controls.Add(this.menuStrip1);
            this.MainMenuStrip = this.menuStrip1;
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "MainApplication";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainApplication_FormClosing);
            this.Load += new System.EventHandler(this.MainApplication_Load);
            this.cmdContext.ResumeLayout(false);
            this.logsContext.ResumeLayout(false);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.columntrackBar)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rowtrackBar)).EndInit();
            this.splitContainer.Panel1.ResumeLayout(false);
            this.splitContainer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).EndInit();
            this.splitContainer.ResumeLayout(false);
            this.tabControl2.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.MenuStrip menuStrip1;
        private System.Windows.Forms.ToolStripMenuItem 系统设置ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 创建客户ToolStripMenuItem;
        private UserControls.UListView logList;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel1;
        private System.Windows.Forms.ToolStripStatusLabel stripHost;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel3;
        private System.Windows.Forms.ToolStripStatusLabel stripPort;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel5;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel6;
        private System.Windows.Forms.ToolStripStatusLabel stripConnectedNum;
        private System.Windows.Forms.ContextMenuStrip cmdContext;
        private System.Windows.Forms.ToolStripMenuItem 发送信息ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 备注更改ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 会话管理ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 关闭计算机ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 重启计算机ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem 开机启动ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 取消自启动ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 隐藏服务端ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripMenuItem 卸载控制端ToolStripMenuItem;
        private System.Windows.Forms.ContextMenuStrip logsContext;
        private System.Windows.Forms.ToolStripMenuItem 复制日志ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 删除日志ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator4;
        private System.Windows.Forms.ToolStripMenuItem 选择全部ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 取消选择ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 清空日志ToolStripMenuItem1;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.ToolStripMenuItem 查看ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem statusToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem3;
        private System.Windows.Forms.ToolStripMenuItem onlineToolStripMenuItem;
        private System.Windows.Forms.FlowLayoutPanel desktopViewLayout;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem4;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem5;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.TrackBar rowtrackBar;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.LinkLabel linkLabel1;
        private System.Windows.Forms.Label fsd;
        private System.Windows.Forms.Label viewRow;
        private System.Windows.Forms.TrackBar columntrackBar;
        private System.Windows.Forms.Label viewColumn;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripButton toolStripButton7;
        private System.Windows.Forms.ToolStripButton toolStripButton6;
        private System.Windows.Forms.ToolStripButton toolStripButton8;
        private System.Windows.Forms.ToolStripButton toolStripButton10;
        private System.Windows.Forms.ToolStripButton toolStripButton9;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator5;
        private System.Windows.Forms.ToolStripMenuItem ToolStripMenuItem;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel2;
        private System.Windows.Forms.ToolStripStatusLabel struflow;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel7;
        private System.Windows.Forms.ToolStripStatusLabel strdownflow;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel4;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel8;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem6;
        private System.Windows.Forms.ToolStripButton toolStripButton14;
        private System.Windows.Forms.ToolStripMenuItem 锁定ToolStripMenuItem;
        private System.Windows.Forms.SplitContainer splitContainer;
        private System.Windows.Forms.TabControl tabControl2;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.TabPage tabPage1;
        private UserControls.UListView servicesOnlineList;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem7;
        private System.Windows.Forms.ComboBox groupBox;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.ToolStripMenuItem updateClient;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator6;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem8;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator8;
        private System.Windows.Forms.ToolStripMenuItem installServiceMenuItem;
        private System.Windows.Forms.ToolStripMenuItem unInstallServiceMenuItem;
        private System.Windows.Forms.LinkLabel linkLabel2;
    }
}

